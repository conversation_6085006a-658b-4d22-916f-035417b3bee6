# Ikonnect API - Voucher Platform

A Spring Boot Kotlin application for managing a voucher platform with data plans, user management, and voucher distribution.

## 🏗️ Project Structure

```
ikonnect-api/
├── build.gradle.kts                 # Gradle build configuration
├── src/
│   ├── main/
│   │   ├── kotlin/
│   │   │   └── com/ikonnected/ikonnectapi/
│   │   │       ├── IkonnectApiApplication.kt    # Main application entry point
│   │   │       ├── models/                       # Data models and entities
│   │   │       │   ├── User.kt                  # User entity with roles and status
│   │   │       │   ├── DataPlan.kt              # Data plan configuration
│   │   │       │   ├── Voucher.kt               # Voucher entity for data plans
│   │   │       │   ├── PlanSize.kt              # Plan size definitions
│   │   │       │   ├── Roles.kt                 # User role enums
│   │   │       │   ├── UserStatus.kt            # User status enums
│   │   │       │   ├── VoucherStatus.kt         # Voucher status enums
│   │   │       │   └── Status.kt                # General status enums
│   │   │       ├── controllers/                  # REST API controllers (empty)
│   │   │       └── repositories/                 # Data access layer (empty)
│   │   └── resources/
│   │       ├── application.properties            # Application configuration
│   │       ├── static/                          # Static resources
│   │       └── templates/                       # Template files
│   └── test/
│       └── kotlin/com/ikonnected/ikonnectapi/
│           └── IkonnectApiApplicationTests.kt   # Basic application tests
├── gradle/                                       # Gradle wrapper files
├── gradlew                                       # Gradle wrapper script (Unix)
├── gradlew.bat                                   # Gradle wrapper script (Windows)
└── Voucher Platform.docx                         # Project documentation
```

## 🚀 Technology Stack

- **Language**: Kotlin 1.9.25
- **Framework**: Spring Boot 3.5.4
- **Database**: MongoDB (with reactive support)
- **Build Tool**: Gradle with Kotlin DSL
- **Java Version**: 17
- **Security**: Spring Security with JWT
- **Documentation**: Spring REST Docs

## 📦 Dependencies

### Core Dependencies
- `spring-boot-starter-web` - Web application support
- `spring-boot-starter-data-mongodb` - MongoDB integration
- `spring-boot-starter-data-mongodb-reactive` - Reactive MongoDB support
- `spring-boot-starter-data-rest` - REST repository support
- `spring-boot-starter-hateoas` - HATEOAS support
- `spring-boot-starter-security` - Security framework
- `spring-boot-starter-validation` - Validation support

### Security & Authentication
- `spring-security-crypto` - Password encryption
- `io.jsonwebtoken:jjwt-api` - JWT token handling
- `io.jsonwebtoken:jjwt-impl` - JWT implementation
- `io.jsonwebtoken:jjwt-jackson` - JWT Jackson support

### Kotlin Support
- `kotlin-reflect` - Kotlin reflection
- `kotlinx-coroutines-reactor` - Coroutines for reactive programming
- `jackson-module-kotlin` - Jackson Kotlin support

## 🗄️ Data Models

### User Model (`User.kt`)
- **Purpose**: Represents users in the system with different roles
- **Key Fields**:
  - `id`: MongoDB ObjectId
  - `firstName`, `lastName`: User names
  - `email`: Unique email address (indexed)
  - `username`: Optional unique store name (sparse index)
  - `phoneNumber`: Optional phone number (sparse index)
  - `password`: Hashed password
  - `role`: User role (MASTER_ADMIN or MINI_ADMIN)
  - `status`: User status (ACTIVE or PAUSED)
  - `createdBy`: Links mini admin to master admin
  - `platformActive`: Store activation status
  - `catalogueMessage`: Store notification message

### Data Plan Model (`DataPlan.kt`)
- **Purpose**: Defines data plans that can be purchased
- **Key Fields**:
  - `id`: MongoDB ObjectId
  - `ownerId`: Admin who created the plan
  - `name`: Plan name
  - `planSizeId`: Reference to plan size
  - `numberOfUsers`: Number of users the plan supports
  - `price`: Plan price
  - `validityInDays`: Plan validity period

### Voucher Model (`Voucher.kt`)
- **Purpose**: Represents vouchers for data plan purchases
- **Key Fields**:
  - `id`: MongoDB ObjectId
  - `code`: Unique voucher code (indexed)
  - `dataPlanId`: Associated data plan
  - `ownerId`: Admin who owns the voucher
  - `status`: Voucher status (UNSOLD or SOLD)
  - `transactionId`: Payment gateway transaction ID
  - `uploadedAt`: Creation timestamp
  - `soldAt`: Sale timestamp

### Plan Size Model (`PlanSize.kt`)
- **Purpose**: Defines different plan sizes/categories
- **Key Fields**:
  - `id`: MongoDB ObjectId
  - `ownerId`: Admin who created the size
  - `name`: Display name (e.g., "5GB", "100GB")

## 🔐 Security & Roles

### User Roles
- **MASTER_ADMIN**: Full system access, can create mini admins
- **MINI_ADMIN**: Limited access, manages their own store/vouchers

### User Status
- **ACTIVE**: User can access the platform
- **PAUSED**: User access is temporarily suspended

### Voucher Status
- **UNSOLD**: Voucher is available for purchase
- **SOLD**: Voucher has been purchased

## ⚙️ Configuration

### Application Properties (`application.properties`)
- **Application Name**: `ikonnect-api`
- **Server Port**: `5425`
- **MongoDB URI**: Configurable via environment variable `MONGODB_CONNECTION_STRING`
  - Default: `mongodb://localhost:27017/ikonnect`

## 🏃‍♂️ Running the Application

### Prerequisites
- Java 17 or higher
- MongoDB instance running
- Gradle (or use the included wrapper)

### Development Setup
1. Clone the repository
2. Ensure MongoDB is running
3. Set up MongoDB connection string (optional, defaults to localhost)
4. Run the application:
   ```bash
   ./gradlew bootRun
   ```

### Building
```bash
./gradlew build
```

### Testing
```bash
./gradlew test
```

## 📋 Current State

### ✅ Implemented
- Basic Spring Boot application structure
- MongoDB data models with proper indexing
- User management with roles and status
- Data plan and voucher management
- Security framework setup
- JWT token support

### 🚧 TODO
- REST API controllers
- Repository implementations
- Authentication and authorization logic
- Business logic implementation
- API documentation
- Comprehensive test coverage

## 🔍 Database Collections

The application uses the following MongoDB collections:
- `users` - User accounts and profiles
- `data_plans` - Data plan definitions
- `vouchers` - Voucher codes and status
- `plan_sizes` - Plan size categories

## 📝 Notes

- The application uses MongoDB with reactive support for scalability
- JWT tokens are used for authentication
- Password hashing is implemented for security
- Sparse indexes are used for optional fields to allow multiple null values
- The platform supports a hierarchical admin structure (Master Admin → Mini Admin)

## 📚 Documentation

- `Voucher Platform.docx` - Detailed project requirements and specifications
- `HELP.md` - Spring Boot reference documentation links 