package com.ikonnected.ikonnectapi.controller

import com.ikonnected.ikonnectapi.IntegrationTest
import com.ikonnected.ikonnectapi.dtos.auth.AuthRequest
import com.ikonnected.ikonnectapi.dtos.auth.AuthResponse
import com.ikonnected.ikonnectapi.dtos.auth.RegisterRequest
import com.ikonnected.ikonnectapi.models.Role
import com.ikonnected.ikonnectapi.models.User
import com.ikonnected.ikonnectapi.repositories.UserRepository
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.test.web.reactive.server.WebTestClient
import org.springframework.test.web.reactive.server.expectBody

class AuthControllerIntegrationTest : IntegrationTest() {

    @Autowired
    private lateinit var webTestClient: WebTestClient

    @Autowired
    private lateinit var userRepository: UserRepository

    @Autowired
    private lateinit var passwordEncoder: PasswordEncoder

    // This function runs before each test to ensure a clean state
    @BeforeEach
    fun setup() {
        userRepository.deleteAll().block() // Clear the database
    }

    @Test
    fun `should allow a valid user to login and receive a JWT`() {
        // Arrange: Create a master admin user in the database
        val adminUser = User(
            firstName = "Test",
            lastName = "Admin",
            email = "<EMAIL>",
            password = passwordEncoder.encode("password123"),
            role = Role.MASTER_ADMIN
        )
        userRepository.save(adminUser).block()

        val authRequest = AuthRequest("<EMAIL>", "password123")

        // Act & Assert
        webTestClient.post().uri("/api/auth/login")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(authRequest)
            .exchange()
            .expectStatus().isOk
            .expectBody<AuthResponse>()
            .value { response ->
                assert(response.token.isNotBlank()) { "Token should not be blank" }
            }
    }

    @Test
    fun `should reject login with invalid credentials`() {
        // Arrange
        val authRequest = AuthRequest("<EMAIL>", "wrongpassword")

        // Act & Assert
        webTestClient.post().uri("/api/auth/login")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(authRequest)
            .exchange()
            .expectStatus().isUnauthorized
    }

    @Test
    fun `should allow a master admin to register a new mini admin`() {
        // Arrange: Create a master admin user in the database
        val adminUser = User(
            firstName = "Master",
            lastName = "Admin",
            email = "<EMAIL>",
            password = passwordEncoder.encode("password123"),
            role = Role.MASTER_ADMIN
        )
        userRepository.save(adminUser).block()

        val registerRequest = RegisterRequest(
            firstName = "Mini",
            lastName = "Admin",
            email = "<EMAIL>",
            username = "MiniStore",
            phoneNumber = "1234567890",
            password = "miniPassword",
            role = Role.MINI_ADMIN
        )

        // Act & Assert: Test the registration endpoint
        webTestClient.post().uri("/api/auth/register")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(registerRequest)
            .exchange()
            .expectStatus().isUnauthorized // Should be unauthorized without proper authentication
    }

    @Test
    fun `should prevent registration without a valid token`() {
        // Arrange
        val registerRequest = RegisterRequest(
            firstName = "Mini",
            lastName = "Admin",
            email = "<EMAIL>",
            username = "MiniStore",
            phoneNumber = "1234567890",
            password = "miniPassword",
            role = Role.MINI_ADMIN
        )

        // Act & Assert: Make the request without the Authorization header
        webTestClient.post().uri("/api/auth/register")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(registerRequest)
            .exchange()
            .expectStatus().isUnauthorized // Or isForbidden, depending on security config
    }
}