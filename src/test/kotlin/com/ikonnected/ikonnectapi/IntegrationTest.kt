package com.ikonnected.ikonnectapi

import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestPropertySource

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient
@TestPropertySource(
    properties = [
        "spring.data.mongodb.uri=mongodb://localhost:27017/ikonnect-test",
        "application.security.jwt.secret-key=test-secret-key-for-integration-tests-is-long-enough",
        "application.security.jwt.expiration=86400000"
    ]
)
abstract class IntegrationTest {
    // Using local MongoDB instead of Testcontainers
    // Make sure MongoDB is running locally on port 27017
}