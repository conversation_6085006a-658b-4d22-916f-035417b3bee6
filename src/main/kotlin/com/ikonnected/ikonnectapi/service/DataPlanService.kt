package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.dtos.dataplan.CreateDataPlanRequest
import com.ikonnected.ikonnectapi.dtos.dataplan.DataPlanListResponse
import com.ikonnected.ikonnectapi.dtos.dataplan.DataPlanResponse
import com.ikonnected.ikonnectapi.dtos.dataplan.UpdateDataPlanRequest
import com.ikonnected.ikonnectapi.models.DataPlan
import com.ikonnected.ikonnectapi.repositories.DataPlanRepository
import com.ikonnected.ikonnectapi.repositories.PlanSizeRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.time.Instant

@Service
class DataPlanService(
    private val dataPlanRepository: DataPlanRepository,
    private val planSizeRepository: PlanSizeRepository
) {

    fun createDataPlan(request: CreateDataPlanRequest, ownerId: ObjectId): Mono<DataPlanResponse> {
        val planSizeObjectId = ObjectId(request.planSizeId)
       // Validate plan size exists and belongs to owner
        return planSizeRepository.findByOwnerIdAndId(ownerId, planSizeObjectId)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Plan size not found or access denied")))
            .flatMap { planSize ->
                // Check if plan name already exists for this owner
                dataPlanRepository.existsByOwnerIdAndName(ownerId, request.name)
                    .flatMap { exists ->
                        if (exists) {
                            Mono.error<DataPlan>(IllegalArgumentException("Data plan with name '${request.name}' already exists"))
                        } else {
                            val dataPlan = DataPlan(
                                ownerId = ownerId,
                                name = request.name,
                                planSizeId = planSizeObjectId,
                                numberOfUsers = request.numberOfUsers,
                                price = request.price,
                                validityInDays = request.validityInDays
                            )
                            dataPlanRepository.save(dataPlan)
                        }
                    }
                    .map { savedPlan ->
                        DataPlanResponse(
                            id = savedPlan.id.toHexString(),
                            ownerId = savedPlan.ownerId.toHexString(),
                            name = savedPlan.name,
                            planSizeId = savedPlan.planSizeId.toHexString(),
                            planSizeName = planSize.name,
                            numberOfUsers = savedPlan.numberOfUsers,
                            price = savedPlan.price,
                            validityInDays = savedPlan.validityInDays,
                            createdAt = savedPlan.createdAt,
                            updatedAt = savedPlan.updatedAt
                        )
                    }
            }
    }

    fun getDataPlansByOwner(ownerId: ObjectId): Mono<DataPlanListResponse> {
        return dataPlanRepository.findByOwnerId(ownerId)
            .flatMap { dataPlan ->
                planSizeRepository.findById(dataPlan.planSizeId)
                    .map { planSize ->
                        DataPlanResponse(
                            id = dataPlan.id.toHexString(),
                            ownerId = dataPlan.ownerId.toHexString(),
                            name = dataPlan.name,
                            planSizeId = dataPlan.planSizeId.toHexString(),
                            planSizeName = planSize.name,
                            numberOfUsers = dataPlan.numberOfUsers,
                            price = dataPlan.price,
                            validityInDays = dataPlan.validityInDays,
                            createdAt = dataPlan.createdAt,
                            updatedAt = dataPlan.updatedAt
                        )
                    }
                    .defaultIfEmpty(
                        DataPlanResponse(
                            id = dataPlan.id.toHexString(),
                            ownerId = dataPlan.ownerId.toHexString(),
                            name = dataPlan.name,
                            planSizeId = dataPlan.planSizeId.toHexString(),
                            planSizeName = null,
                            numberOfUsers = dataPlan.numberOfUsers,
                            price = dataPlan.price,
                            validityInDays = dataPlan.validityInDays,
                            createdAt = dataPlan.createdAt,
                            updatedAt = dataPlan.updatedAt
                        )
                    )
            }
            .collectList()
            .map { dataplans ->
                DataPlanListResponse(
                    dataPlans = dataplans,
                    total = dataplans.size.toLong()
                )
            }
    }

    fun getDataPlanById(id: ObjectId, ownerId: ObjectId): Mono<DataPlanResponse> {
        return dataPlanRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                planSizeRepository.findById(dataPlan.planSizeId)
                    .map { planSize ->
                        DataPlanResponse(
                            id = dataPlan.id.toHexString(),
                            ownerId = dataPlan.ownerId.toHexString(),
                            name = dataPlan.name,
                            planSizeId = dataPlan.planSizeId.toHexString(),
                            planSizeName = planSize.name,
                            numberOfUsers = dataPlan.numberOfUsers,
                            price = dataPlan.price,
                            validityInDays = dataPlan.validityInDays,
                            createdAt = dataPlan.createdAt,
                            updatedAt = dataPlan.updatedAt
                        )
                    }
                    .defaultIfEmpty(
                        DataPlanResponse(
                            id = dataPlan.id.toHexString(),
                            ownerId = dataPlan.ownerId.toHexString(),
                            name = dataPlan.name,
                            planSizeId = dataPlan.planSizeId.toHexString(),
                            planSizeName = null,
                            numberOfUsers = dataPlan.numberOfUsers,
                            price = dataPlan.price,
                            validityInDays = dataPlan.validityInDays,
                            createdAt = dataPlan.createdAt,
                            updatedAt = dataPlan.updatedAt
                        )
                    )
            }
    }

    fun updateDataPlan(id: ObjectId, request: UpdateDataPlanRequest, ownerId: ObjectId): Mono<DataPlanResponse> {
        return dataPlanRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { existingPlan ->
                // Validate plan size if provided and get the final plan size info
                val planSizeValidation = if (request.planSizeId != null) {
                    val planSizeObjectId = ObjectId(request.planSizeId)
                    planSizeRepository.findByOwnerIdAndId(ownerId, planSizeObjectId)
                        .switchIfEmpty(Mono.error(IllegalArgumentException("Plan size not found or access denied")))
                        .map { planSize -> planSizeObjectId to planSize.name }
                } else {
                    planSizeRepository.findById(existingPlan.planSizeId)
                        .map { planSize -> existingPlan.planSizeId to planSize.name }
                        .defaultIfEmpty(existingPlan.planSizeId to null)
                }

                planSizeValidation.flatMap { (planSizeId, planSizeName) ->
                    // Check name uniqueness if name is being updated
                    val nameCheck = if (request.name != null && request.name != existingPlan.name) {
                        dataPlanRepository.existsByOwnerIdAndName(ownerId, request.name)
                            .flatMap { exists ->
                                if (exists) {
                                    Mono.error(IllegalArgumentException("Data plan with name '${request.name}' already exists"))
                                } else {
                                    Mono.just(false)
                                }
                            }
                    } else {
                        Mono.just(false)
                    }

                    nameCheck.flatMap {
                        val updatedPlan = existingPlan.copy(
                            name = request.name ?: existingPlan.name,
                            planSizeId = planSizeId,
                            numberOfUsers = request.numberOfUsers ?: existingPlan.numberOfUsers,
                            price = request.price ?: existingPlan.price,
                            validityInDays = request.validityInDays ?: existingPlan.validityInDays,
                            updatedAt = Instant.now()
                        )

                        dataPlanRepository.save(updatedPlan)
                            .map { savedPlan ->
                                DataPlanResponse(
                                    id = savedPlan.id.toHexString(),
                                    ownerId = savedPlan.ownerId.toHexString(),
                                    name = savedPlan.name,
                                    planSizeId = savedPlan.planSizeId.toHexString(),
                                    planSizeName = planSizeName,
                                    numberOfUsers = savedPlan.numberOfUsers,
                                    price = savedPlan.price,
                                    validityInDays = savedPlan.validityInDays,
                                    createdAt = savedPlan.createdAt,
                                    updatedAt = savedPlan.updatedAt
                                )
                            }
                    }
                }
            }
    }

    fun deleteDataPlan(id: ObjectId, ownerId: ObjectId): Mono<Void> {
        return dataPlanRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlanRepository.delete(it) }
    }
}
