package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.dtos.plansize.*
import com.ikonnected.ikonnectapi.models.PlanSize
import com.ikonnected.ikonnectapi.repositories.PlanSizeRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.time.Instant

@Service
class PlanSizeService(private val planSizeRepository: PlanSizeRepository) {

    fun createPlanSize(request: CreatePlanSizeRequest, ownerId: ObjectId): Mono<PlanSizeResponse> {
        // Check if plan size name already exists for this owner
        return planSizeRepository.existsByOwnerIdAndName(ownerId, request.name)
            .flatMap { exists ->
                if (exists) {
                    Mono.error<PlanSize>(IllegalArgumentException("Plan size with name '${request.name}' already exists"))
                } else {
                    val planSize = PlanSize(
                        ownerId = ownerId,
                        name = request.name
                    )
                    planSizeRepository.save(planSize)
                }
            }
            .map { savedPlanSize ->
                PlanSizeResponse(
                    id = savedPlanSize.id.toHexString(),
                    ownerId = savedPlanSize.ownerId.toHexString(),
                    name = savedPlanSize.name,
                    createdAt = savedPlanSize.createdAt
                )
            }
    }

    fun getPlanSizesByOwner(ownerId: ObjectId): Mono<PlanSizeListResponse> {
        return planSizeRepository.findByOwnerId(ownerId)
            .map { planSize ->
                PlanSizeResponse(
                    id = planSize.id.toHexString(),
                    ownerId = planSize.ownerId.toHexString(),
                    name = planSize.name,
                    createdAt = planSize.createdAt
                )
            }
            .collectList()
            .map { planSizes ->
                PlanSizeListResponse(
                    planSizes = planSizes,
                    total = planSizes.size.toLong()
                )
            }
    }

    fun getPlanSizeById(id: ObjectId, ownerId: ObjectId): Mono<PlanSizeResponse> {
        return planSizeRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Plan size not found or access denied")))
            .map { planSize ->
                PlanSizeResponse(
                    id = planSize.id.toHexString(),
                    ownerId = planSize.ownerId.toHexString(),
                    name = planSize.name,
                    createdAt = planSize.createdAt
                )
            }
    }

    fun updatePlanSize(id: ObjectId, request: UpdatePlanSizeRequest, ownerId: ObjectId): Mono<PlanSizeResponse> {
        return planSizeRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Plan size not found or access denied")))
            .flatMap { existingPlanSize ->
                // Check name uniqueness if name is being updated
                val nameCheck = if (request.name != null && request.name != existingPlanSize.name) {
                    planSizeRepository.existsByOwnerIdAndName(ownerId, request.name)
                        .flatMap { exists ->
                            if (exists) {
                                Mono.error<Boolean>(IllegalArgumentException("Plan size with name '${request.name}' already exists"))
                            } else {
                                Mono.just(false)
                            }
                        }
                } else {
                    Mono.just(false)
                }

                nameCheck.flatMap {
                    val updatedPlanSize = existingPlanSize.copy(
                        name = request.name ?: existingPlanSize.name
                    )
                    
                    planSizeRepository.save(updatedPlanSize)
                        .map { savedPlanSize ->
                            PlanSizeResponse(
                                id = savedPlanSize.id.toHexString(),
                                ownerId = savedPlanSize.ownerId.toHexString(),
                                name = savedPlanSize.name,
                                createdAt = savedPlanSize.createdAt
                            )
                        }
                }
            }
    }

    fun deletePlanSize(id: ObjectId, ownerId: ObjectId): Mono<Void> {
        return planSizeRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Plan size not found or access denied")))
            .flatMap { planSizeRepository.delete(it) }
    }
}
