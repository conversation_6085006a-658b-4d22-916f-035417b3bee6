package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.models.User
import com.ikonnected.ikonnectapi.repositories.UserRepository
import org.bson.types.ObjectId
import org.springframework.security.core.Authentication
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class UserContextService(private val userRepository: UserRepository) {

    /**
     * Extracts the current user's ObjectId from the authentication context
     */
    fun getCurrentUserId(authentication: Authentication): Mono<ObjectId> {
        return getCurrentUser(authentication)
            .map { it.id }
    }

    /**
     * Extracts the current user from the authentication context
     */
    fun getCurrentUser(authentication: Authentication): Mono<User> {
        val userDetails = authentication.principal as? UserDetails
            ?: return Mono.error(IllegalStateException("No authenticated user found"))
        
        val email = userDetails.username
        return userRepository.findByEmail(email)
            .switchIfEmpty(Mono.error(IllegalStateException("Authenticated user not found in database")))
    }

    /**
     * Checks if the current user has the specified role
     */
    fun hasRole(authentication: Authentication, role: String): Mono<Boolean> {
        val userDetails = authentication.principal as? UserDetails
            ?: return Mono.just(false)
        
        return Mono.just(userDetails.authorities.any { it.authority == "ROLE_$role" })
    }
}
