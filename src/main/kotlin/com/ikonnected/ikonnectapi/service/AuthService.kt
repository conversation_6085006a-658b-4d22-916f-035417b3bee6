package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.dtos.auth.AuthRequest
import com.ikonnected.ikonnectapi.dtos.auth.AuthResponse
import com.ikonnected.ikonnectapi.dtos.auth.RegisterRequest
import com.ikonnected.ikonnectapi.models.Role
import com.ikonnected.ikonnectapi.models.User
import com.ikonnected.ikonnectapi.repositories.UserRepository
import com.ikonnected.ikonnectapi.security.JwtService
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.ReactiveAuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class AuthService(
    private val userRepository: UserRepository,
    private val passwordEncoder: PasswordEncoder,
    private val jwtService: JwtService,
    private val authenticationManager: ReactiveAuthenticationManager
) {

    fun registerMiniAdmin(request: RegisterRequest): Mono<User> {
        // In a real app, you'd get the creator's ID from the security context
        // For now, we'll assume a master admin is making the request.
        val miniAdmin = User(
            firstName = request.firstName,
            lastName = request.lastName,
            email = request.email,
            username = request.username,
            phoneNumber = request.phoneNumber,
            password = passwordEncoder.encode(request.password),
            role = Role.MINI_ADMIN,
            // createdBy = masterAdminId // TODO: Get this from security context
        )
        return userRepository.save(miniAdmin)
    }

    fun login(request: AuthRequest): Mono<AuthResponse> {
        val authentication = UsernamePasswordAuthenticationToken(request.email, request.password)

        return authenticationManager.authenticate(authentication)
            .switchIfEmpty(Mono.error(BadCredentialsException("Invalid email or password")))
            .flatMap { authResult ->
                userRepository.findByEmail(authResult.name).map { user ->
                    // The user details object from CustomUserDetailsService
                    val userDetails = org.springframework.security.core.userdetails.User
                        .withUsername(user.email)
                        .password(user.password)
                        .roles(user.role.name)
                        .build()
                    val jwtToken = jwtService.generateToken(userDetails)
                    AuthResponse(token = jwtToken)
                }
            }
    }
}