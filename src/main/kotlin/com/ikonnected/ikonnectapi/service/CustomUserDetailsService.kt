package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.repositories.UserRepository
import org.springframework.security.core.userdetails.ReactiveUserDetailsService
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import org.springframework.security.core.userdetails.User as SpringUser

@Service
class CustomUserDetailsService(private val userRepository: UserRepository) : ReactiveUserDetailsService {

    override fun findByUsername(username: String): Mono<UserDetails> {
        return userRepository.findByEmail(username)
            .switchIfEmpty(Mono.error(UsernameNotFoundException("User not found with email: $username")))
            .map { user ->
                SpringUser.withUsername(user.email)
                    .password(user.password)
                    .roles(user.role.name) // Spring Security automatically prepends "ROLE_"
                    .accountLocked(user.status != com.ikonnected.ikonnectapi.models.UserStatus.ACTIVE)
                    .build()
            }
    }
}