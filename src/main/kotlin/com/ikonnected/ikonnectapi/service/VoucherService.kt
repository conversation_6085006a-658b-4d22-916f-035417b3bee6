package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.dtos.voucher.*
import com.ikonnected.ikonnectapi.models.Voucher
import com.ikonnected.ikonnectapi.models.VoucherStatus
import com.ikonnected.ikonnectapi.repositories.DataPlanRepository
import com.ikonnected.ikonnectapi.repositories.VoucherRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.time.Instant

@Service
class VoucherService(
    private val voucherRepository: VoucherRepository,
    private val dataPlanRepository: DataPlanRepository
) {

    /**
     * Creates multiple vouchers with admin-uploaded 6-digit codes
     * Validates that all codes are unique and follow the 6-digit format
     */
    fun createVouchers(request: CreateVouchersRequest, ownerId: ObjectId): Mono<BulkVoucherCreateResponse> {
        val dataPlanObjectId = ObjectId(request.dataPlanId)

        // Validate data plan exists and belongs to owner
        return dataPlanRepository.findByOwnerIdAndId(ownerId, dataPlanObjectId)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                /* Check for duplicate codes in the request */
                val duplicatesInRequest = request.voucherCodes.groupBy { it }.filter { it.value.size > 1 }.keys
                if (duplicatesInRequest.isNotEmpty()) {
                    return@flatMap Mono.error<BulkVoucherCreateResponse>(
                        IllegalArgumentException(
                            "Duplicate voucher codes in request: ${
                                duplicatesInRequest.joinToString(
                                    ", "
                                )
                            }"
                        )
                    )
                }

                // Check for existing codes in database
                Flux.fromIterable(request.voucherCodes)
                    .flatMap { code ->
                        voucherRepository.existsByCode(code)
                            .map { exists -> code to exists }
                    }
                    .collectList()
                    .flatMap { codeExistenceList ->
                        val existingCodes = codeExistenceList.filter { it.second }.map { it.first }

                        if (existingCodes.isNotEmpty()) {
                            Mono.error(
                                IllegalArgumentException("Voucher codes already exist: ${existingCodes.joinToString(", ")}")
                            )
                        } else {
                            // Create vouchers with uploaded codes
                            val vouchers = request.voucherCodes.map { code ->
                                Voucher(
                                    code = code,
                                    dataPlanId = dataPlanObjectId,
                                    ownerId = ownerId
                                )
                            }

                            // Save all vouchers
                            voucherRepository.saveAll(vouchers)
                                .map { savedVoucher ->
                                    createVoucherResponse(savedVoucher, dataPlan.name)
                                }
                                .collectList()
                                .map { voucherResponses ->
                                    BulkVoucherCreateResponse(
                                        createdVouchers = voucherResponses,
                                        totalCreated = voucherResponses.size
                                    )
                                }
                        }
                    }
            }
    }

    /**
     * Creates a single voucher with admin-uploaded 6-digit code
     * Validates that the code is unique and follows the 6-digit format
     */
    fun createSingleVoucher(request: CreateSingleVoucherRequest, ownerId: ObjectId): Mono<VoucherResponse> {
        val dataPlanObjectId = ObjectId(request.dataPlanId)

        // Validate data plan exists and belongs to owner
        return dataPlanRepository.findByOwnerIdAndId(ownerId, dataPlanObjectId)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                // Check if voucher code already exists
                voucherRepository.existsByCode(request.code)
                    .flatMap { exists ->
                        if (exists) {
                            Mono.error(IllegalArgumentException("Voucher code '${request.code}' already exists"))
                        } else {
                            val voucher = Voucher(
                                code = request.code,
                                dataPlanId = dataPlanObjectId,
                                ownerId = ownerId
                            )
                            voucherRepository.save(voucher)
                        }
                    }
                    .map { savedVoucher ->
                        createVoucherResponse(savedVoucher, dataPlan.name)
                    }
            }
    }

    fun getVouchersByOwner(ownerId: ObjectId, status: VoucherStatus?): Mono<VoucherListResponse> {
        val vouchersFlux = if (status != null) {
            voucherRepository.findByOwnerIdAndStatus(ownerId, status)
        } else {
            voucherRepository.findByOwnerId(ownerId)
        }

        return vouchersFlux
            .flatMap { voucher ->
                dataPlanRepository.findById(voucher.dataPlanId)
                    .map { dataPlan -> createVoucherResponse(voucher, dataPlan.name) }
                    .defaultIfEmpty(createVoucherResponse(voucher, null))
            }
            .collectList()
            .zipWith(voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.UNSOLD))
            .zipWith(voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.SOLD))
            .map { tuple ->
                val vouchersAndUnsold = tuple.t1
                val soldCount = tuple.t2
                val vouchers = vouchersAndUnsold.t1
                val unsoldCount = vouchersAndUnsold.t2

                VoucherListResponse(
                    vouchers = vouchers,
                    total = vouchers.size.toLong(),
                    unsoldCount = unsoldCount,
                    soldCount = soldCount
                )
            }
    }


    private fun createVoucherResponse(voucher: Voucher, dataPlanName: String?): VoucherResponse {
        return VoucherResponse(
            id = voucher.id.toHexString(),
            code = voucher.code,
            dataPlanId = voucher.dataPlanId.toHexString(),
            dataPlanName = dataPlanName,
            ownerId = voucher.ownerId.toHexString(),
            status = voucher.status,
            transactionId = voucher.transactionId,
            uploadedAt = voucher.uploadedAt,
            soldAt = voucher.soldAt
        )
    }

    fun getVoucherById(id: ObjectId, ownerId: ObjectId): Mono<VoucherResponse> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                dataPlanRepository.findById(voucher.dataPlanId)
                    .map { dataPlan -> createVoucherResponse(voucher, dataPlan.name) }
                    .defaultIfEmpty(createVoucherResponse(voucher, null))
            }
    }

    fun sellVoucher(id: ObjectId, request: SellVoucherRequest, ownerId: ObjectId): Mono<VoucherResponse> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                if (voucher.status == VoucherStatus.SOLD) {
                    Mono.error(IllegalArgumentException("Voucher is already sold"))
                } else {
                    val updatedVoucher = voucher.copy(
                        status = VoucherStatus.SOLD,
                        transactionId = request.transactionId,
                        soldAt = Instant.now()
                    )
                    voucherRepository.save(updatedVoucher)
                }
            }
            .flatMap { soldVoucher ->
                dataPlanRepository.findById(soldVoucher.dataPlanId)
                    .map { dataPlan -> createVoucherResponse(soldVoucher, dataPlan.name) }
                    .defaultIfEmpty(createVoucherResponse(soldVoucher, null))
            }
    }

    fun deleteVoucher(id: ObjectId, ownerId: ObjectId): Mono<Void> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                if (voucher.status == VoucherStatus.SOLD) {
                    Mono.error(IllegalArgumentException("Cannot delete sold voucher"))
                } else {
                    voucherRepository.delete(voucher)
                }
            }
    }

    fun getVoucherStats(ownerId: ObjectId): Mono<VoucherStatsResponse> {
        return Mono.zip(
            voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.UNSOLD),
            voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.SOLD)
        ).map { tuple ->
            val unsoldCount = tuple.t1
            val soldCount = tuple.t2
            VoucherStatsResponse(
                totalVouchers = unsoldCount + soldCount,
                unsoldVouchers = unsoldCount,
                soldVouchers = soldCount
            )
        }
    }
}
