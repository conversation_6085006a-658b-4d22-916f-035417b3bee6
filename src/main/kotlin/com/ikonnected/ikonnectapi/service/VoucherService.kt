package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.dtos.voucher.*
import com.ikonnected.ikonnectapi.models.Voucher
import com.ikonnected.ikonnectapi.models.VoucherStatus
import com.ikonnected.ikonnectapi.repositories.DataPlanRepository
import com.ikonnected.ikonnectapi.repositories.VoucherRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.time.Instant
import java.util.*

@Service
class VoucherService(
    private val voucherRepository: VoucherRepository,
    private val dataPlanRepository: DataPlanRepository
) {

    fun createVouchers(request: CreateVoucherRequest, ownerId: ObjectId): Mono<BulkVoucherCreateResponse> {
        val dataPlanObjectId = ObjectId(request.dataPlanId)
        
        // Validate data plan exists and belongs to owner
        return dataPlanRepository.findByOwnerIdAndId(ownerId, dataPlanObjectId)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                // Generate unique voucher codes
                val voucherCodes = generateUniqueVoucherCodes(request.quantity)
                
                // Create vouchers
                val vouchers = voucherCodes.map { code ->
                    Voucher(
                        code = code,
                        dataPlanId = dataPlanObjectId,
                        ownerId = ownerId
                    )
                }
                
                // Save all vouchers
                voucherRepository.saveAll(vouchers)
                    .map { savedVoucher ->
                        VoucherResponse(
                            id = savedVoucher.id.toHexString(),
                            code = savedVoucher.code,
                            dataPlanId = savedVoucher.dataPlanId.toHexString(),
                            dataPlanName = dataPlan.name,
                            ownerId = savedVoucher.ownerId.toHexString(),
                            status = savedVoucher.status,
                            transactionId = savedVoucher.transactionId,
                            uploadedAt = savedVoucher.uploadedAt,
                            soldAt = savedVoucher.soldAt
                        )
                    }
                    .collectList()
                    .map { voucherResponses ->
                        BulkVoucherCreateResponse(
                            createdVouchers = voucherResponses,
                            totalCreated = voucherResponses.size
                        )
                    }
            }
    }

    fun createSingleVoucher(request: CreateSingleVoucherRequest, ownerId: ObjectId): Mono<VoucherResponse> {
        val dataPlanObjectId = ObjectId(request.dataPlanId)
        
        // Validate data plan exists and belongs to owner
        return dataPlanRepository.findByOwnerIdAndId(ownerId, dataPlanObjectId)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                // Check if voucher code already exists
                voucherRepository.existsByCode(request.code)
                    .flatMap { exists ->
                        if (exists) {
                            Mono.error<Voucher>(IllegalArgumentException("Voucher code '${request.code}' already exists"))
                        } else {
                            val voucher = Voucher(
                                code = request.code,
                                dataPlanId = dataPlanObjectId,
                                ownerId = ownerId
                            )
                            voucherRepository.save(voucher)
                        }
                    }
                    .map { savedVoucher ->
                        VoucherResponse(
                            id = savedVoucher.id.toHexString(),
                            code = savedVoucher.code,
                            dataPlanId = savedVoucher.dataPlanId.toHexString(),
                            dataPlanName = dataPlan.name,
                            ownerId = savedVoucher.ownerId.toHexString(),
                            status = savedVoucher.status,
                            transactionId = savedVoucher.transactionId,
                            uploadedAt = savedVoucher.uploadedAt,
                            soldAt = savedVoucher.soldAt
                        )
                    }
            }
    }

    fun getVouchersByOwner(ownerId: ObjectId, status: VoucherStatus?): Mono<VoucherListResponse> {
        val vouchersFlux = if (status != null) {
            voucherRepository.findByOwnerIdAndStatus(ownerId, status)
        } else {
            voucherRepository.findByOwnerId(ownerId)
        }
        
        return vouchersFlux
            .flatMap { voucher ->
                dataPlanRepository.findById(voucher.dataPlanId)
                    .map { dataPlan ->
                        VoucherResponse(
                            id = voucher.id.toHexString(),
                            code = voucher.code,
                            dataPlanId = voucher.dataPlanId.toHexString(),
                            dataPlanName = dataPlan.name,
                            ownerId = voucher.ownerId.toHexString(),
                            status = voucher.status,
                            transactionId = voucher.transactionId,
                            uploadedAt = voucher.uploadedAt,
                            soldAt = voucher.soldAt
                        )
                    }
                    .defaultIfEmpty(
                        VoucherResponse(
                            id = voucher.id.toHexString(),
                            code = voucher.code,
                            dataPlanId = voucher.dataPlanId.toHexString(),
                            dataPlanName = null,
                            ownerId = voucher.ownerId.toHexString(),
                            status = voucher.status,
                            transactionId = voucher.transactionId,
                            uploadedAt = voucher.uploadedAt,
                            soldAt = voucher.soldAt
                        )
                    )
            }
            .collectList()
            .zipWith(voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.UNSOLD))
            .zipWith(voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.SOLD))
            .map { tuple ->
                val (vouchersAndUnsold, soldCount) = tuple
                val (vouchers, unsoldCount) = vouchersAndUnsold
                
                VoucherListResponse(
                    vouchers = vouchers,
                    total = vouchers.size.toLong(),
                    unsoldCount = unsoldCount,
                    soldCount = soldCount
                )
            }
    }

    private fun generateUniqueVoucherCodes(quantity: Int): List<String> {
        return (1..quantity).map {
            generateVoucherCode()
        }
    }

    private fun generateVoucherCode(): String {
        // Generate a 12-digit voucher code
        val random = Random()
        return (1..12).map { random.nextInt(10) }.joinToString("")
    }

    fun getVoucherById(id: ObjectId, ownerId: ObjectId): Mono<VoucherResponse> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                dataPlanRepository.findById(voucher.dataPlanId)
                    .map { dataPlan ->
                        VoucherResponse(
                            id = voucher.id.toHexString(),
                            code = voucher.code,
                            dataPlanId = voucher.dataPlanId.toHexString(),
                            dataPlanName = dataPlan.name,
                            ownerId = voucher.ownerId.toHexString(),
                            status = voucher.status,
                            transactionId = voucher.transactionId,
                            uploadedAt = voucher.uploadedAt,
                            soldAt = voucher.soldAt
                        )
                    }
                    .defaultIfEmpty(
                        VoucherResponse(
                            id = voucher.id.toHexString(),
                            code = voucher.code,
                            dataPlanId = voucher.dataPlanId.toHexString(),
                            dataPlanName = null,
                            ownerId = voucher.ownerId.toHexString(),
                            status = voucher.status,
                            transactionId = voucher.transactionId,
                            uploadedAt = voucher.uploadedAt,
                            soldAt = voucher.soldAt
                        )
                    )
            }
    }

    fun sellVoucher(id: ObjectId, request: SellVoucherRequest, ownerId: ObjectId): Mono<VoucherResponse> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                if (voucher.status == VoucherStatus.SOLD) {
                    Mono.error<Voucher>(IllegalArgumentException("Voucher is already sold"))
                } else {
                    val updatedVoucher = voucher.copy(
                        status = VoucherStatus.SOLD,
                        transactionId = request.transactionId,
                        soldAt = Instant.now()
                    )
                    voucherRepository.save(updatedVoucher)
                }
            }
            .flatMap { soldVoucher ->
                dataPlanRepository.findById(soldVoucher.dataPlanId)
                    .map { dataPlan ->
                        VoucherResponse(
                            id = soldVoucher.id.toHexString(),
                            code = soldVoucher.code,
                            dataPlanId = soldVoucher.dataPlanId.toHexString(),
                            dataPlanName = dataPlan.name,
                            ownerId = soldVoucher.ownerId.toHexString(),
                            status = soldVoucher.status,
                            transactionId = soldVoucher.transactionId,
                            uploadedAt = soldVoucher.uploadedAt,
                            soldAt = soldVoucher.soldAt
                        )
                    }
                    .defaultIfEmpty(
                        VoucherResponse(
                            id = soldVoucher.id.toHexString(),
                            code = soldVoucher.code,
                            dataPlanId = soldVoucher.dataPlanId.toHexString(),
                            dataPlanName = null,
                            ownerId = soldVoucher.ownerId.toHexString(),
                            status = soldVoucher.status,
                            transactionId = soldVoucher.transactionId,
                            uploadedAt = soldVoucher.uploadedAt,
                            soldAt = soldVoucher.soldAt
                        )
                    )
            }
    }

    fun deleteVoucher(id: ObjectId, ownerId: ObjectId): Mono<Void> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(Mono.error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                if (voucher.status == VoucherStatus.SOLD) {
                    Mono.error<Void>(IllegalArgumentException("Cannot delete sold voucher"))
                } else {
                    voucherRepository.delete(voucher)
                }
            }
    }

    fun getVoucherStats(ownerId: ObjectId): Mono<VoucherStatsResponse> {
        return Mono.zip(
            voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.UNSOLD),
            voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.SOLD)
        ).map { (unsoldCount, soldCount) ->
            VoucherStatsResponse(
                totalVouchers = unsoldCount + soldCount,
                unsoldVouchers = unsoldCount,
                soldVouchers = soldCount
            )
        }
    }
}
