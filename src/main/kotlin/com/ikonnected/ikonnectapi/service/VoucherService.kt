package com.ikonnected.ikonnectapi.service

import com.ikonnected.ikonnectapi.dtos.voucher.*
import com.ikonnected.ikonnectapi.models.Voucher
import com.ikonnected.ikonnectapi.models.VoucherStatus
import com.ikonnected.ikonnectapi.repositories.DataPlanRepository
import com.ikonnected.ikonnectapi.repositories.VoucherRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.core.publisher.Mono.error
import reactor.kotlin.core.util.function.component1
import reactor.kotlin.core.util.function.component2
import java.time.Instant
import java.util.*

@Service
class VoucherService(
    private val voucherRepository: VoucherRepository,
    private val dataPlanRepository: DataPlanRepository
) {

    fun createVouchers(request: CreateVoucherRequest, ownerId: ObjectId): Mono<BulkVoucherCreateResponse> {
        val dataPlanObjectId = ObjectId(request.dataPlanId)

        // Validate data plan exists and belongs to owner
        return dataPlanRepository.findByOwnerIdAndId(ownerId, dataPlanObjectId)
            .switchIfEmpty(error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                // Generate unique voucher codes
                val voucherCodes = generateUniqueVoucherCodes(request.quantity)

                // Create vouchers
                val vouchers = voucherCodes.map { code ->
                    Voucher(
                        code = code,
                        dataPlanId = dataPlanObjectId,
                        ownerId = ownerId
                    )
                }

                // Save all vouchers
                voucherRepository.saveAll(vouchers)
                    .map { savedVoucher ->
                        createVoucherResponse(savedVoucher, dataPlan.name)
                    }
                    .collectList()
                    .map { voucherResponses ->
                        BulkVoucherCreateResponse(
                            createdVouchers = voucherResponses,
                            totalCreated = voucherResponses.size
                        )
                    }
            }
    }

    fun createSingleVoucher(request: CreateSingleVoucherRequest, ownerId: ObjectId): Mono<VoucherResponse> {
        val dataPlanObjectId = ObjectId(request.dataPlanId)

        // Validate data plan exists and belongs to owner
        return dataPlanRepository.findByOwnerIdAndId(ownerId, dataPlanObjectId)
            .switchIfEmpty(error(IllegalArgumentException("Data plan not found or access denied")))
            .flatMap { dataPlan ->
                // Check if voucher code already exists
                voucherRepository.existsByCode(request.code)
                    .flatMap { exists ->
                        if (exists) {
                            error(IllegalArgumentException("Voucher code '${request.code}' already exists"))
                        } else {
                            val voucher = Voucher(
                                code = request.code,
                                dataPlanId = dataPlanObjectId,
                                ownerId = ownerId
                            )
                            voucherRepository.save(voucher)
                        }
                    }
                    .map { savedVoucher ->
                        createVoucherResponse(savedVoucher, dataPlan.name)
                    }
            }
    }

    fun getVouchersByOwner(ownerId: ObjectId, status: VoucherStatus?): Mono<VoucherListResponse> {
        val vouchersFlux = if (status != null) {
            voucherRepository.findByOwnerIdAndStatus(ownerId, status)
        } else {
            voucherRepository.findByOwnerId(ownerId)
        }

        return vouchersFlux
            .flatMap { voucher ->
                dataPlanRepository.findById(voucher.dataPlanId)
                    .map { dataPlan -> createVoucherResponse(voucher, dataPlan.name) }
                    .defaultIfEmpty(createVoucherResponse(voucher, null))
            }
            .collectList()
            .zipWith(voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.UNSOLD))
            .zipWith(voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.SOLD))
            .map { tuple ->
                val (vouchersAndUnsold, soldCount) = tuple
                val (vouchers, unsoldCount) = vouchersAndUnsold

                VoucherListResponse(
                    vouchers = vouchers,
                    total = vouchers.size.toLong(),
                    unsoldCount = unsoldCount,
                    soldCount = soldCount
                )
            }
    }

    private fun generateUniqueVoucherCodes(quantity: Int): List<String> {
        return (1..quantity).map {
            generateVoucherCode()
        }
    }

    private fun generateVoucherCode(): String {
        // Generate a 12-digit voucher code
        val random = Random()
        return (1..12).map { random.nextInt(10) }.joinToString("")
    }

    private fun createVoucherResponse(voucher: Voucher, dataPlanName: String?): VoucherResponse {
        return VoucherResponse(
            id = voucher.id.toHexString(),
            code = voucher.code,
            dataPlanId = voucher.dataPlanId.toHexString(),
            dataPlanName = dataPlanName,
            ownerId = voucher.ownerId.toHexString(),
            status = voucher.status,
            transactionId = voucher.transactionId,
            uploadedAt = voucher.uploadedAt,
            soldAt = voucher.soldAt
        )
    }

    fun getVoucherById(id: ObjectId, ownerId: ObjectId): Mono<VoucherResponse> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                dataPlanRepository.findById(voucher.dataPlanId)
                    .map { dataPlan -> createVoucherResponse(voucher, dataPlan.name) }
                    .defaultIfEmpty(createVoucherResponse(voucher, null))
            }
    }

    fun sellVoucher(id: ObjectId, request: SellVoucherRequest, ownerId: ObjectId): Mono<VoucherResponse> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                if (voucher.status == VoucherStatus.SOLD) {
                    error(IllegalArgumentException("Voucher is already sold"))
                } else {
                    val updatedVoucher = voucher.copy(
                        status = VoucherStatus.SOLD,
                        transactionId = request.transactionId,
                        soldAt = Instant.now()
                    )
                    voucherRepository.save(updatedVoucher)
                }
            }
            .flatMap { soldVoucher ->
                dataPlanRepository.findById(soldVoucher.dataPlanId)
                    .map { dataPlan -> createVoucherResponse(soldVoucher, dataPlan.name) }
                    .defaultIfEmpty(createVoucherResponse(soldVoucher, null))
            }
    }

    fun deleteVoucher(id: ObjectId, ownerId: ObjectId): Mono<Void> {
        return voucherRepository.findByOwnerIdAndId(ownerId, id)
            .switchIfEmpty(error(IllegalArgumentException("Voucher not found or access denied")))
            .flatMap { voucher ->
                if (voucher.status == VoucherStatus.SOLD) {
                    error(IllegalArgumentException("Cannot delete sold voucher"))
                } else {
                    voucherRepository.delete(voucher)
                }
            }
    }

    fun getVoucherStats(ownerId: ObjectId): Mono<VoucherStatsResponse> {
        return Mono.zip(
            voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.UNSOLD),
            voucherRepository.countByOwnerIdAndStatus(ownerId, VoucherStatus.SOLD)
        ).map { (unsoldCount, soldCount) ->
            VoucherStatsResponse(
                totalVouchers = unsoldCount + soldCount,
                unsoldVouchers = unsoldCount,
                soldVouchers = soldCount
            )
        }
    }
}
