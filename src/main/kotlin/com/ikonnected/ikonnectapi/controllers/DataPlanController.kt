package com.ikonnected.ikonnectapi.controllers

import com.ikonnected.ikonnectapi.dtos.dataplan.*
import com.ikonnected.ikonnectapi.service.DataPlanService
import com.ikonnected.ikonnectapi.service.UserContextService
import jakarta.validation.Valid
import org.bson.types.ObjectId
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/data-plans")
@PreAuthorize("hasRole('MASTER_ADMIN') or hasRole('MINI_ADMIN')")
class DataPlanController(
    private val dataPlanService: DataPlanService,
    private val userContextService: UserContextService
) {

    @PostMapping
    fun createDataPlan(
        @Valid @RequestBody request: CreateDataPlanRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<DataPlanResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                dataPlanService.createDataPlan(request, ownerId)
            }
            .map { ResponseEntity.status(HttpStatus.CREATED).body(it) }
            .onErrorResume { error ->
                when (error) {
                    is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                    else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                }
            }
    }

    @GetMapping
    fun getDataPlans(authentication: Authentication): Mono<ResponseEntity<DataPlanListResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                dataPlanService.getDataPlansByOwner(ownerId)
            }
            .map { ResponseEntity.ok(it) }
            .onErrorResume {
                Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
            }
    }

    @GetMapping("/{id}")
    fun getDataPlan(
        @PathVariable id: String,
        authentication: Authentication
    ): Mono<ResponseEntity<DataPlanResponse>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    dataPlanService.getDataPlanById(objectId, ownerId)
                }
                .map { ResponseEntity.ok(it) }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.notFound().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @PutMapping("/{id}")
    fun updateDataPlan(
        @PathVariable id: String,
        @Valid @RequestBody request: UpdateDataPlanRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<DataPlanResponse>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    dataPlanService.updateDataPlan(objectId, request, ownerId)
                }
                .map { ResponseEntity.ok(it) }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @DeleteMapping("/{id}")
    fun deleteDataPlan(
        @PathVariable id: String,
        authentication: Authentication
    ): Mono<ResponseEntity<Void>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    dataPlanService.deleteDataPlan(objectId, ownerId)
                }
                .map { ResponseEntity.noContent().build<Void>() }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.notFound().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }
}
