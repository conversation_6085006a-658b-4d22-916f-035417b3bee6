package com.ikonnected.ikonnectapi.controllers

import com.ikonnected.ikonnectapi.dtos.voucher.*
import com.ikonnected.ikonnectapi.models.VoucherStatus
import com.ikonnected.ikonnectapi.service.UserContextService
import com.ikonnected.ikonnectapi.service.VoucherService
import jakarta.validation.Valid
import org.bson.types.ObjectId
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/vouchers")
@PreAuthorize("hasRole('MASTER_ADMIN') or hasRole('MINI_ADMIN')")
class VoucherController(
    private val voucherService: VoucherService,
    private val userContextService: UserContextService
) {

    @PostMapping("/bulk")
    fun createVouchers(
        @Valid @RequestBody request: CreateVoucherRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<BulkVoucherCreateResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                voucherService.createVouchers(request, ownerId)
            }
            .map { ResponseEntity.status(HttpStatus.CREATED).body(it) }
            .onErrorResume { error ->
                when (error) {
                    is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                    else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                }
            }
    }

    @PostMapping("/single")
    fun createSingleVoucher(
        @Valid @RequestBody request: CreateSingleVoucherRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<VoucherResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                voucherService.createSingleVoucher(request, ownerId)
            }
            .map { ResponseEntity.status(HttpStatus.CREATED).body(it) }
            .onErrorResume { error ->
                when (error) {
                    is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                    else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                }
            }
    }

    @GetMapping
    fun getVouchers(
        @RequestParam(required = false) status: VoucherStatus?,
        authentication: Authentication
    ): Mono<ResponseEntity<VoucherListResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                voucherService.getVouchersByOwner(ownerId, status)
            }
            .map { ResponseEntity.ok(it) }
            .onErrorResume {
                Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
            }
    }

    @GetMapping("/{id}")
    fun getVoucher(
        @PathVariable id: String,
        authentication: Authentication
    ): Mono<ResponseEntity<VoucherResponse>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    voucherService.getVoucherById(objectId, ownerId)
                }
                .map { ResponseEntity.ok(it) }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.notFound().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @PostMapping("/{id}/sell")
    fun sellVoucher(
        @PathVariable id: String,
        @Valid @RequestBody request: SellVoucherRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<VoucherResponse>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    voucherService.sellVoucher(objectId, request, ownerId)
                }
                .map { ResponseEntity.ok(it) }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @DeleteMapping("/{id}")
    fun deleteVoucher(
        @PathVariable id: String,
        authentication: Authentication
    ): Mono<ResponseEntity<Void>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    voucherService.deleteVoucher(objectId, ownerId)
                }
                .map { ResponseEntity.noContent().build<Void>() }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @GetMapping("/stats")
    fun getVoucherStats(authentication: Authentication): Mono<ResponseEntity<VoucherStatsResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                voucherService.getVoucherStats(ownerId)
            }
            .map { ResponseEntity.ok(it) }
            .onErrorResume {
                Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
            }
    }
}
