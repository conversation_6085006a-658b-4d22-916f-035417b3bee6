package com.ikonnected.ikonnectapi.controllers

import com.ikonnected.ikonnectapi.dtos.auth.AuthRequest
import com.ikonnected.ikonnectapi.dtos.auth.AuthResponse
import com.ikonnected.ikonnectapi.dtos.auth.RegisterRequest
import com.ikonnected.ikonnectapi.models.Role
import com.ikonnected.ikonnectapi.service.AuthService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/auth")
class AuthController(private val authService: AuthService) {

    @PostMapping("/login")
    fun login(@RequestBody authRequest: AuthRequest): Mono<ResponseEntity<AuthResponse>> {
        return authService.login(authRequest)
            .map { ResponseEntity.ok(it) }
            .onErrorResume {
                Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).build())
            }
    }

    @PostMapping("/register")
    @PreAuthorize("hasRole('MASTER_ADMIN')") // Only Master Admins can register Mini Admins
    fun register(@RequestBody registerRequest: RegisterRequest): Mono<ResponseEntity<*>> {
        if (registerRequest.role != Role.MINI_ADMIN) {
            return Mono.just(
                ResponseEntity.badRequest().body("Registration is only allowed for MINI_ADMIN role.")
            )
        }
        return authService.registerMiniAdmin(registerRequest)
            .map { user ->
                ResponseEntity.status(HttpStatus.CREATED).body(
                    mapOf("message" to "Mini Admin registered successfully", "userId" to user.id.toHexString())
                )
            }
    }
}