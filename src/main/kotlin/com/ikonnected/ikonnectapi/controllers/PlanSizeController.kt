package com.ikonnected.ikonnectapi.controllers

import com.ikonnected.ikonnectapi.dtos.plansize.*
import com.ikonnected.ikonnectapi.service.PlanSizeService
import com.ikonnected.ikonnectapi.service.UserContextService
import jakarta.validation.Valid
import org.bson.types.ObjectId
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/plan-sizes")
@PreAuthorize("hasRole('MASTER_ADMIN') or hasRole('MINI_ADMIN')")
class PlanSizeController(
    private val planSizeService: PlanSizeService,
    private val userContextService: UserContextService
) {

    @PostMapping
    fun createPlanSize(
        @Valid @RequestBody request: CreatePlanSizeRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<PlanSizeResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                planSizeService.createPlanSize(request, ownerId)
            }
            .map { ResponseEntity.status(HttpStatus.CREATED).body(it) }
            .onErrorResume { error ->
                when (error) {
                    is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                    else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                }
            }
    }

    @GetMapping
    fun getPlanSizes(authentication: Authentication): Mono<ResponseEntity<PlanSizeListResponse>> {
        return userContextService.getCurrentUserId(authentication)
            .flatMap { ownerId ->
                planSizeService.getPlanSizesByOwner(ownerId)
            }
            .map { ResponseEntity.ok(it) }
            .onErrorResume {
                Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
            }
    }

    @GetMapping("/{id}")
    fun getPlanSize(
        @PathVariable id: String,
        authentication: Authentication
    ): Mono<ResponseEntity<PlanSizeResponse>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    planSizeService.getPlanSizeById(objectId, ownerId)
                }
                .map { ResponseEntity.ok(it) }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.notFound().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @PutMapping("/{id}")
    fun updatePlanSize(
        @PathVariable id: String,
        @Valid @RequestBody request: UpdatePlanSizeRequest,
        authentication: Authentication
    ): Mono<ResponseEntity<PlanSizeResponse>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    planSizeService.updatePlanSize(objectId, request, ownerId)
                }
                .map { ResponseEntity.ok(it) }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.badRequest().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }

    @DeleteMapping("/{id}")
    fun deletePlanSize(
        @PathVariable id: String,
        authentication: Authentication
    ): Mono<ResponseEntity<Void>> {
        return try {
            val objectId = ObjectId(id)
            userContextService.getCurrentUserId(authentication)
                .flatMap { ownerId ->
                    planSizeService.deletePlanSize(objectId, ownerId)
                }
                .map { ResponseEntity.noContent().build<Void>() }
                .onErrorResume { error ->
                    when (error) {
                        is IllegalArgumentException -> Mono.just(ResponseEntity.notFound().build())
                        else -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                    }
                }
        } catch (e: IllegalArgumentException) {
            Mono.just(ResponseEntity.badRequest().build())
        }
    }
}
