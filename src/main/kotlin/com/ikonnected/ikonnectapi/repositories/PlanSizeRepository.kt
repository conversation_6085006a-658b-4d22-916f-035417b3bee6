package com.ikonnected.ikonnectapi.repositories

import com.ikonnected.ikonnectapi.models.PlanSize
import org.bson.types.ObjectId
import org.springframework.data.mongodb.repository.ReactiveMongoRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Repository
interface PlanSizeRepository : ReactiveMongoRepository<PlanSize, ObjectId> {
    fun findByOwnerId(ownerId: ObjectId): Flux<PlanSize>
    fun findByOwnerIdAndId(ownerId: ObjectId, id: ObjectId): Mono<PlanSize>
    fun existsByOwnerIdAndName(ownerId: ObjectId, name: String): Mono<Boolean>
}
