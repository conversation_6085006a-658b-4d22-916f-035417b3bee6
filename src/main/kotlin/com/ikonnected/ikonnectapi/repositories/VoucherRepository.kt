package com.ikonnected.ikonnectapi.repositories

import com.ikonnected.ikonnectapi.models.Voucher
import com.ikonnected.ikonnectapi.models.VoucherStatus
import org.bson.types.ObjectId
import org.springframework.data.mongodb.repository.ReactiveMongoRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Repository
interface VoucherRepository : ReactiveMongoRepository<Voucher, ObjectId> {
    fun findByOwnerId(ownerId: ObjectId): Flux<Voucher>
    fun findByOwnerIdAndId(ownerId: ObjectId, id: ObjectId): Mono<Voucher>
    fun findByDataPlanId(dataPlanId: ObjectId): Flux<Voucher>
    fun findByOwnerIdAndDataPlanId(ownerId: ObjectId, dataPlanId: ObjectId): Flux<Voucher>
    fun findByOwnerIdAndStatus(ownerId: ObjectId, status: VoucherStatus): Flux<Voucher>
    fun findByCode(code: String): Mono<Voucher>
    fun existsByCode(code: String): Mono<Boolean>
    fun countByOwnerIdAndStatus(ownerId: ObjectId, status: VoucherStatus): Mono<Long>
    fun countByDataPlanIdAndStatus(dataPlanId: ObjectId, status: VoucherStatus): Mono<Long>
}
