package com.ikonnected.ikonnectapi.repositories

import com.ikonnected.ikonnectapi.models.DataPlan
import org.bson.types.ObjectId
import org.springframework.data.mongodb.repository.ReactiveMongoRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Repository
interface DataPlanRepository : ReactiveMongoRepository<DataPlan, ObjectId> {
    fun findByOwnerId(ownerId: ObjectId): Flux<DataPlan>
    fun findByOwnerIdAndId(ownerId: ObjectId, id: ObjectId): Mono<DataPlan>
    fun findByPlanSizeId(planSizeId: ObjectId): Flux<DataPlan>
    fun existsByOwnerIdAndName(ownerId: ObjectId, name: String): Mono<Boolean>
}
