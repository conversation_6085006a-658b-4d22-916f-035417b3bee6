package com.ikonnected.ikonnectapi.repositories

import com.ikonnected.ikonnectapi.models.Role
import com.ikonnected.ikonnectapi.models.User
import org.bson.types.ObjectId
import org.springframework.data.mongodb.repository.ReactiveMongoRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Mono

@Repository
interface UserRepository : ReactiveMongoRepository<User, ObjectId> {
    fun findByEmail(email: String): Mono<User>
    fun existsByRole(role: Role): Mono<Boolean>
}