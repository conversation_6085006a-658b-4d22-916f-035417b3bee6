package com.ikonnected.ikonnectapi.config

import com.ikonnected.ikonnectapi.models.Role
import com.ikonnected.ikonnectapi.models.User
import com.ikonnected.ikonnectapi.repositories.UserRepository
import org.slf4j.LoggerFactory
import org.springframework.boot.CommandLineRunner
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono

@Component
class DataInitializer(
    private val userRepository: UserRepository,
    private val passwordEncoder: PasswordEncoder
) : CommandLineRunner {

    private val logger = LoggerFactory.getLogger(DataInitializer::class.java)

    override fun run(vararg args: String?) {
        // Use `existsByRole` which correctly returns a Mono<Boolean>.
        // This is more efficient than finding the whole user object.
        userRepository.existsByRole(Role.MASTER_ADMIN)
            .flatMap { isAdminExists ->
                if (!isAdminExists) {
                    logger.info("No MASTER_ADMIN found. Creating initial admin account...")
                    val masterAdmin = User(
                        firstName = "Master",
                        lastName = "Admin",
                        email = "<EMAIL>",
                        password = passwordEncoder.encode("DefaultPassword123!"), // CHANGE THIS IN PRODUCTION
                        role = Role.MASTER_ADMIN
                    )
                    userRepository.save(masterAdmin)
                } else {
                    logger.info("MASTER_ADMIN account already exists.")
                    // Return an empty Mono to signify completion without a value.
                    Mono.empty<User>()
                }
            }
            .subscribe(
                { user -> logger.info("Successfully created MASTER_ADMIN with email: ${user.email}") },
                { error -> logger.error("Error during data initialization", error) },
                { logger.info("Data initialization check complete.") } // Log completion
            )
    }
}