package com.ikonnected.ikonnectapi.models

import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document(collection = "plan_sizes")
data class PlanSize(
    @Id val id: ObjectId = ObjectId.get(),
    val ownerId: ObjectId, // The Master or Mini Admin who created this size
    val name: String,      // The display name, e.g., "5GB", "100GB", "Weekend Special"
    val createdAt: Instant = Instant.now()
)