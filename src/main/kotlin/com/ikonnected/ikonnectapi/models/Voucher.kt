package com.ikonnected.ikonnectapi.models

import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document(collection = "vouchers")
data class Voucher(
    @Id val id: ObjectId = ObjectId.get(),
    @Indexed(unique = true) val code: String,
    val dataPlanId: ObjectId,
    val ownerId: ObjectId,
    var status: VoucherStatus = VoucherStatus.UNSOLD,
    var transactionId: String? = null, // The payment gateway's transaction ID for the purchase
    val uploadedAt: Instant = Instant.now(),
    var soldAt: Instant? = null
)