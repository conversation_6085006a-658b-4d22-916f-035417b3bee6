package com.ikonnected.ikonnectapi.models

import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document(collection = "data_plans")
data class DataPlan(
    @Id val id: ObjectId = ObjectId.get(),
    val ownerId: ObjectId, //Master or Mini <PERSON> who created this plan
    val name: String,
    val planSizeId: ObjectId, // Changed from planSize: PlanSize to reference the ID
    val numberOfUsers: Int,
    val price: Double,
    val validityInDays: Validity,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now()
)
