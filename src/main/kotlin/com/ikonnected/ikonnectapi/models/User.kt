package com.ikonnected.ikonnectapi.models

import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document(collection = "users")
data class User(
    @Id val id: ObjectId = ObjectId.get(),
    val firstName: String,
    val lastName: String,
    @Indexed(unique = true) val email: String,
    @Indexed(unique = true, sparse = true) val username: String? = null, // Name of mini merchant store, sparse index allows multiple nulls
    @Indexed(unique = true, sparse = true)  val phoneNumber: String? = null,
    val password: String, // Hashed passwords are stored as Strings
    val role: Role,
    val status: UserStatus = UserStatus.ACTIVE,


    // --- Admin-specific fields ---
    val createdBy: ObjectId? = null, // Links a Mini Admin to the Master Admin who created them
    val platformActive: Boolean = true, // For Master <PERSON><PERSON> to turn a Mini Admin's store on/off
    val catalogueMessage: String? = null, // Notification message for a Mini Admin's catalogue page

    val createdAt: Instant = Instant.now()
)