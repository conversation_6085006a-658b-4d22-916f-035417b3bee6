package com.ikonnected.ikonnectapi.dtos.dataplan

import com.ikonnected.ikonnectapi.models.Validity
import jakarta.validation.constraints.*

data class UpdateDataPlanRequest(
    @field:Size(min = 2, max = 100, message = "Plan name must be between 2 and 100 characters")
    val name: String?,
    
    val planSizeId: String?, // Will be converted to ObjectId
    
    @field:Min(value = 1, message = "Number of users must be at least 1")
    @field:Max(value = 1000, message = "Number of users cannot exceed 1000")
    val numberOfUsers: Int?,
    
    @field:DecimalMin(value = "0.01", message = "Price must be greater than 0")
    @field:DecimalMax(value = "999999.99", message = "Price cannot exceed 999,999.99")
    val price: Double?,
    
    val validityInDays: Validity?
)
