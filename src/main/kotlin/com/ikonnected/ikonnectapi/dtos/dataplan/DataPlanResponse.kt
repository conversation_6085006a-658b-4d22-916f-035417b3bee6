package com.ikonnected.ikonnectapi.dtos.dataplan

import com.ikonnected.ikonnectapi.models.Validity
import java.time.Instant

data class DataPlanResponse(
    val id: String,
    val ownerId: String,
    val name: String,
    val planSizeId: String,
    val planSizeName: String?, // Populated from PlanSize lookup
    val numberOfUsers: Int,
    val price: Double,
    val validityInDays: Validity,
    val createdAt: Instant,
    val updatedAt: Instant
)

data class DataPlanListResponse(
    val dataPlans: List<DataPlanResponse>,
    val total: Long
)
