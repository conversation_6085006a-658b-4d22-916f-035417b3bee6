package com.ikonnected.ikonnectapi.dtos.voucher

import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank

data class CreateVoucherRequest(
    @field:NotBlank(message = "Data plan ID is required")
    val dataPlanId: String,
    
    @field:Min(value = 1, message = "Quantity must be at least 1")
    val quantity: Int = 1
)

data class CreateSingleVoucherRequest(
    @field:NotBlank(message = "Data plan ID is required")
    val dataPlanId: String,
    
    @field:NotBlank(message = "Voucher code is required")
    val code: String
)
