package com.ikonnected.ikonnectapi.dtos.voucher

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size

data class CreateVouchersRequest(
    @field:NotBlank(message = "Data plan ID is required")
    val dataPlanId: String,

    @field:NotEmpty(message = "At least one voucher code is required")
    @field:Valid
    val voucherCodes: List<@Pattern(
        regexp = "^[0-9]{6}$",
        message = "Voucher code must be exactly 6 digits"
    ) String>
)

data class CreateSingleVoucherRequest(
    @field:NotBlank(message = "Data plan ID is required")
    val dataPlanId: String,

    @field:NotBlank(message = "Voucher code is required")
    @field:Pattern(
        regexp = "^[0-9]{6}$",
        message = "Voucher code must be exactly 6 digits"
    )
    val code: String
)
