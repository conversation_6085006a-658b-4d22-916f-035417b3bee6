package com.ikonnected.ikonnectapi.dtos.voucher

import com.ikonnected.ikonnectapi.models.VoucherStatus
import java.time.Instant

data class VoucherResponse(
    val id: String,
    val code: String,
    val dataPlanId: String,
    val dataPlanName: String?,
    val ownerId: String,
    val status: VoucherStatus,
    val transactionId: String?,
    val uploadedAt: Instant,
    val soldAt: Instant?
)

data class VoucherListResponse(
    val vouchers: List<VoucherResponse>,
    val total: Long,
    val unsoldCount: Long,
    val soldCount: Long
)

data class VoucherStatsResponse(
    val totalVouchers: Long,
    val unsoldVouchers: Long,
    val soldVouchers: Long,
    val totalRevenue: Double? = null
)

data class SellVoucherRequest(
    val transactionId: String
)

data class BulkVoucherCreateResponse(
    val createdVouchers: List<VoucherResponse>,
    val totalCreated: Int,
    val failedCodes: List<String> = emptyList()
)
