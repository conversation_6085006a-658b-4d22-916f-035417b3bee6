

**Name domain/brand:** 

* iconnect (Iconnect Swift Services)

**Master Admin Functions:**

* All Elements of Mini Admin  
* Able to Create/Setup Accounts for Mini Admins  
* Pause/Resume, Delete Mini Admins Account  
* Push Notification Message to Mini Admins to pass info

**Mini Admins**

* Create Data Bundle Catalogues (Named with size, validity, number of users) and Set price  
* Update/Delete Catalogues  
* Upload/Edit/Delete Voucher codes  
* Track unsold/sold vouchers (Voucher number, date and time)  
* Turn on and turn off voucher selling platforms  
* Print mini notification message onto catalogue page for users to read.

**Users**

* Access catalogue page  
* Input Network provider keyword to access catalogue  
* Select (one or more) vouchers to buy (in one or multiple)  
* Able to see total amount before paying  
* Get a printed voucher on the page after successful payment, an easy copy button and option to save voucher number as image. (and also get code in email for reference)

**Payment Option:**

* Paystack  
* Pay with Opay

**Subscription Features:**

* Plan Name  
* Data Size  
* Validity   
* Number of users/Connections  
* Price

**Onboarding Features:**

* Admin should only be able to register on web only  
* Admin can login to their dashboard on mobile  
  


**Checkout Features:**

* Users should be able to increase the number of vouchers at the checkout screen.  
* Users should be able to download vouchers after successful checkout.




| Subscription: {number\_users: 3,validity: 1\- daily, 7 \-weekly, 14 \- bi-weekly, 15 \> Monthlyvoucher:\[**********,**********,**********,**********,82874872892,\],} |
| :---- |

